import React from 'react';
import {
    Chip,
    Box,
    Typography,
    Tooltip,
    Badge,
    useTheme,
    alpha,
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import {
    CheckCircle as CompletedIcon,
    Schedule as ScheduledIcon,
    PlayArrow as InProgressIcon,
    Warning as WarningIcon,
    Error as ErrorIcon,
    Pause as PausedIcon,
    Cancel as CancelledIcon,
    FiberNew as NewIcon,
} from '@mui/icons-material';

// Cosmic animations
const pulse = keyframes`
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
`;

const glow = keyframes`
    0%, 100% {
        box-shadow: 0 0 5px currentColor;
    }
    50% {
        box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    }
`;

const shimmer = keyframes`
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
`;

// Status configurations
const statusConfig = {
    // Service Order Statuses
    new: {
        color: '#2196f3',
        icon: NewIcon,
        label: 'New',
        description: 'Newly created service order',
        animate: true,
    },
    scheduled: {
        color: '#ff9800',
        icon: ScheduledIcon,
        label: 'Scheduled',
        description: 'Service order scheduled for execution',
        animate: false,
    },
    in_progress: {
        color: '#4caf50',
        icon: InProgressIcon,
        label: 'In Progress',
        description: 'Service order currently being executed',
        animate: true,
    },
    completed: {
        color: '#8bc34a',
        icon: CompletedIcon,
        label: 'Completed',
        description: 'Service order successfully completed',
        animate: false,
    },
    cancelled: {
        color: '#f44336',
        icon: CancelledIcon,
        label: 'Cancelled',
        description: 'Service order has been cancelled',
        animate: false,
    },
    
    // Priority Levels
    low: {
        color: '#4caf50',
        icon: null,
        label: 'Low',
        description: 'Low priority task',
        animate: false,
    },
    medium: {
        color: '#ff9800',
        icon: null,
        label: 'Medium',
        description: 'Medium priority task',
        animate: false,
    },
    high: {
        color: '#f44336',
        icon: WarningIcon,
        label: 'High',
        description: 'High priority task',
        animate: false,
    },
    urgent: {
        color: '#9c27b0',
        icon: ErrorIcon,
        label: 'Urgent',
        description: 'Urgent task requiring immediate attention',
        animate: true,
    },
    
    // Equipment Statuses
    active: {
        color: '#4caf50',
        icon: CompletedIcon,
        label: 'Active',
        description: 'Equipment is operational',
        animate: false,
    },
    inactive: {
        color: '#757575',
        icon: PausedIcon,
        label: 'Inactive',
        description: 'Equipment is not operational',
        animate: false,
    },
    needs_service: {
        color: '#f44336',
        icon: WarningIcon,
        label: 'Needs Service',
        description: 'Equipment requires maintenance',
        animate: true,
    },
    replaced: {
        color: '#ff9800',
        icon: null,
        label: 'Replaced',
        description: 'Equipment has been replaced',
        animate: false,
    },
    
    // Technician Statuses
    available: {
        color: '#4caf50',
        icon: CompletedIcon,
        label: 'Available',
        description: 'Technician is available for work',
        animate: false,
    },
    busy: {
        color: '#ff9800',
        icon: InProgressIcon,
        label: 'Busy',
        description: 'Technician is currently working',
        animate: true,
    },
    on_leave: {
        color: '#757575',
        icon: PausedIcon,
        label: 'On Leave',
        description: 'Technician is on leave',
        animate: false,
    },
};

// Styled Components
const StyledChip = styled(Chip, {
    shouldForwardProp: (prop) => !['animate', 'variant', 'glowing'].includes(prop as string),
})<{
    animate?: boolean;
    variant?: 'default' | 'outlined' | 'filled' | 'gradient';
    glowing?: boolean;
}>(({ theme, animate, variant = 'default', glowing }) => ({
    fontWeight: 600,
    fontSize: '0.75rem',
    height: 28,
    borderRadius: 14,
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    
    // Animation
    ...(animate && {
        animation: `${pulse} 2s ease-in-out infinite`,
    }),
    
    // Glowing effect
    ...(glowing && {
        animation: `${glow} 2s ease-in-out infinite`,
    }),
    
    // Variant styles
    ...(variant === 'gradient' && {
        background: 'linear-gradient(45deg, currentColor, transparent)',
        color: theme.palette.common.white,
    }),
    
    // Hover effects
    '&:hover': {
        transform: 'scale(1.05)',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    
    // Icon spacing
    '& .MuiChip-icon': {
        marginLeft: 8,
        marginRight: -4,
        fontSize: 16,
    },
}));

const StatusDot = styled(Box)<{ color: string; animate?: boolean }>(({ color, animate }) => ({
    width: 8,
    height: 8,
    borderRadius: '50%',
    backgroundColor: color,
    marginRight: 8,
    flexShrink: 0,
    
    ...(animate && {
        animation: `${pulse} 2s ease-in-out infinite`,
    }),
}));

// Component Props
interface CosmicStatusIndicatorProps {
    status: keyof typeof statusConfig;
    variant?: 'chip' | 'dot' | 'badge' | 'text';
    size?: 'small' | 'medium';
    showIcon?: boolean;
    showTooltip?: boolean;
    animate?: boolean;
    glowing?: boolean;
    count?: number;
    onClick?: () => void;
    className?: string;
}

export const CosmicStatusIndicator: React.FC<CosmicStatusIndicatorProps> = ({
    status,
    variant = 'chip',
    size = 'medium',
    showIcon = true,
    showTooltip = true,
    animate,
    glowing = false,
    count,
    onClick,
    className,
}) => {
    const theme = useTheme();
    const config = statusConfig[status];
    
    if (!config) {
        console.warn(`Unknown status: ${status}`);
        return null;
    }
    
    const shouldAnimate = animate !== undefined ? animate : config.animate;
    const IconComponent = config.icon;
    
    const renderContent = () => {
        switch (variant) {
            case 'dot':
                return (
                    <Box display="flex" alignItems="center">
                        <StatusDot color={config.color} animate={shouldAnimate} />
                        <Typography variant="body2" fontWeight={500}>
                            {config.label}
                        </Typography>
                    </Box>
                );
                
            case 'badge':
                return (
                    <Badge
                        badgeContent={count}
                        color="primary"
                        sx={{
                            '& .MuiBadge-badge': {
                                backgroundColor: config.color,
                                color: theme.palette.common.white,
                            },
                        }}
                    >
                        <Box
                            sx={{
                                width: 12,
                                height: 12,
                                borderRadius: '50%',
                                backgroundColor: config.color,
                                ...(shouldAnimate && {
                                    animation: `${pulse} 2s ease-in-out infinite`,
                                }),
                            }}
                        />
                    </Badge>
                );
                
            case 'text':
                return (
                    <Typography
                        variant="body2"
                        sx={{
                            color: config.color,
                            fontWeight: 600,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                        }}
                    >
                        {showIcon && IconComponent && <IconComponent fontSize="small" />}
                        {config.label}
                    </Typography>
                );
                
            default: // chip
                return (
                    <StyledChip
                        label={config.label}
                        icon={showIcon && IconComponent ? <IconComponent /> : undefined}
                        size={size}
                        animate={shouldAnimate}
                        glowing={glowing}
                        onClick={onClick}
                        className={className}
                        sx={{
                            backgroundColor: alpha(config.color, 0.1),
                            color: config.color,
                            border: `1px solid ${alpha(config.color, 0.3)}`,
                            '&:hover': {
                                backgroundColor: alpha(config.color, 0.2),
                            },
                        }}
                    />
                );
        }
    };
    
    const content = renderContent();
    
    if (showTooltip && variant !== 'text') {
        return (
            <Tooltip title={config.description} arrow placement="top">
                <Box display="inline-flex">{content}</Box>
            </Tooltip>
        );
    }
    
    return content;
};

// Specialized Status Components
export const ServiceOrderStatus: React.FC<{
    status: 'new' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
    variant?: 'chip' | 'dot' | 'text';
    showTooltip?: boolean;
}> = ({ status, variant = 'chip', showTooltip = true }) => (
    <CosmicStatusIndicator
        status={status}
        variant={variant}
        showTooltip={showTooltip}
    />
);

export const PriorityIndicator: React.FC<{
    priority: 'low' | 'medium' | 'high' | 'urgent';
    variant?: 'chip' | 'dot' | 'text';
    showTooltip?: boolean;
}> = ({ priority, variant = 'chip', showTooltip = true }) => (
    <CosmicStatusIndicator
        status={priority}
        variant={variant}
        showTooltip={showTooltip}
        glowing={priority === 'urgent'}
    />
);

export const EquipmentStatus: React.FC<{
    status: 'active' | 'inactive' | 'needs_service' | 'replaced';
    variant?: 'chip' | 'dot' | 'text';
    showTooltip?: boolean;
}> = ({ status, variant = 'chip', showTooltip = true }) => (
    <CosmicStatusIndicator
        status={status}
        variant={variant}
        showTooltip={showTooltip}
    />
);

export const TechnicianStatus: React.FC<{
    status: 'available' | 'busy' | 'on_leave';
    variant?: 'chip' | 'dot' | 'text';
    showTooltip?: boolean;
}> = ({ status, variant = 'chip', showTooltip = true }) => (
    <CosmicStatusIndicator
        status={status}
        variant={variant}
        showTooltip={showTooltip}
    />
);
