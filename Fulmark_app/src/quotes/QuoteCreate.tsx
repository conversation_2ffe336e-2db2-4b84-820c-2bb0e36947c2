import {
    Create,
    SimpleForm,
    TextInput,
    SelectInput,
    ReferenceInput,
    AutocompleteInput,
    DateInput,
    NumberInput,
    required,
    useNotify,
    useRedirect,
} from 'react-admin';
import { Grid, Typography, Divider } from '@mui/material';

export const QuoteCreate = () => {
    const notify = useNotify();
    const redirect = useRedirect();

    const onSuccess = () => {
        notify('Quote created successfully');
        redirect('list', 'quotes');
    };

    // Generate quote number (in real app, this would be done on backend)
    const generateQuoteNumber = () => {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `QUO-${year}${month}-${random}`;
    };

    // Calculate valid until date (30 days from now)
    const getDefaultValidUntil = () => {
        const date = new Date();
        date.setDate(date.getDate() + 30);
        return date.toISOString().split('T')[0];
    };

    return (
        <Create mutationOptions={{ onSuccess }}>
            <SimpleForm>
                <Typography variant="h6" gutterBottom>
                    Quote Information
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <TextInput 
                            source="quote_number" 
                            validate={required()} 
                            defaultValue={generateQuoteNumber()}
                            fullWidth 
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <DateInput 
                            source="valid_until" 
                            validate={required()}
                            defaultValue={getDefaultValidUntil()}
                            fullWidth 
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <TextInput source="title" validate={required()} fullWidth />
                    </Grid>
                    <Grid item xs={12}>
                        <TextInput source="description" multiline rows={3} fullWidth />
                    </Grid>
                </Grid>

                <Divider sx={{ my: 3 }} />

                <Typography variant="h6" gutterBottom>
                    Customer Information
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <ReferenceInput source="company_id" reference="companies">
                            <AutocompleteInput optionText="name" fullWidth validate={required()} />
                        </ReferenceInput>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <ReferenceInput source="contact_id" reference="contacts">
                            <AutocompleteInput
                                optionText={(choice: any) => `${choice.first_name} ${choice.last_name}`}
                                fullWidth
                                validate={required()}
                            />
                        </ReferenceInput>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <ReferenceInput source="service_order_id" reference="service_orders">
                            <AutocompleteInput optionText="title" fullWidth />
                        </ReferenceInput>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <ReferenceInput source="technician_id" reference="technicians">
                            <AutocompleteInput
                                optionText={(choice: any) => `${choice.first_name} ${choice.last_name}`}
                                fullWidth
                            />
                        </ReferenceInput>
                    </Grid>
                </Grid>

                <Divider sx={{ my: 3 }} />

                <Typography variant="h6" gutterBottom>
                    Pricing
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                        <NumberInput
                            source="subtotal"
                            label="Subtotal (PLN)"
                            min={0}
                            step={0.01}
                            defaultValue={0}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <NumberInput
                            source="tax_rate"
                            label="Tax Rate (%)"
                            min={0}
                            max={100}
                            step={0.01}
                            defaultValue={23}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <NumberInput
                            source="tax_amount"
                            label="Tax Amount (PLN)"
                            min={0}
                            step={0.01}
                            defaultValue={0}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <NumberInput
                            source="total_amount"
                            label="Total Amount (PLN)"
                            min={0}
                            step={0.01}
                            defaultValue={0}
                            validate={required()}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <SelectInput
                            source="status"
                            choices={[
                                { id: 'draft', name: 'Draft' },
                                { id: 'sent', name: 'Sent' },
                                { id: 'accepted', name: 'Accepted' },
                                { id: 'rejected', name: 'Rejected' },
                                { id: 'expired', name: 'Expired' },
                            ]}
                            defaultValue="draft"
                            validate={required()}
                            fullWidth
                        />
                    </Grid>
                </Grid>

                <Divider sx={{ my: 3 }} />

                <Typography variant="h6" gutterBottom>
                    Terms & Notes
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <TextInput 
                            source="terms_conditions" 
                            label="Terms & Conditions"
                            multiline 
                            rows={4} 
                            fullWidth 
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <TextInput source="notes" multiline rows={3} fullWidth />
                    </Grid>
                </Grid>
            </SimpleForm>
        </Create>
    );
};
