import {
    Create,
    SimpleForm,
    TextInput,
    SelectInput,
    ReferenceInput,
    AutocompleteInput,
    DateTimeInput,
    NumberInput,
    required,
    useNotify,
    useRedirect,
} from 'react-admin';
import { Grid, Typography } from '@mui/material';

export const ServiceOrderCreate = () => {
    const notify = useNotify();
    const redirect = useRedirect();

    const onSuccess = () => {
        notify('Service order created successfully');
        redirect('list', 'service_orders');
    };

    return (
        <Create mutationOptions={{ onSuccess }}>
            <SimpleForm>
                <Typography variant="h6" gutterBottom>
                    Basic Information
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <TextInput source="title" validate={required()} fullWidth />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <SelectInput
                            source="service_type"
                            choices={[
                                { id: 'installation', name: 'Installation' },
                                { id: 'maintenance', name: 'Maintenance' },
                                { id: 'repair', name: 'Repair' },
                                { id: 'inspection', name: 'Inspection' },
                            ]}
                            validate={required()}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <TextInput source="description" multiline rows={3} fullWidth />
                    </Grid>
                </Grid>

                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                    Customer Information
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <ReferenceInput source="company_id" reference="companies">
                            <AutocompleteInput optionText="name" fullWidth validate={required()} />
                        </ReferenceInput>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <ReferenceInput source="contact_id" reference="contacts">
                            <AutocompleteInput
                                optionText={(choice: any) => `${choice.first_name} ${choice.last_name}`}
                                fullWidth
                                validate={required()}
                            />
                        </ReferenceInput>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <ReferenceInput source="equipment_id" reference="equipment">
                            <AutocompleteInput
                                optionText={(choice: any) => `${choice.brand} ${choice.model} (${choice.serial_number || 'No S/N'})`}
                                fullWidth
                            />
                        </ReferenceInput>
                    </Grid>
                </Grid>

                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                    Scheduling & Priority
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                        <SelectInput
                            source="priority"
                            choices={[
                                { id: 'low', name: 'Low' },
                                { id: 'medium', name: 'Medium' },
                                { id: 'high', name: 'High' },
                                { id: 'urgent', name: 'Urgent' },
                            ]}
                            defaultValue="medium"
                            validate={required()}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <SelectInput
                            source="status"
                            choices={[
                                { id: 'new', name: 'New' },
                                { id: 'scheduled', name: 'Scheduled' },
                                { id: 'in_progress', name: 'In Progress' },
                                { id: 'completed', name: 'Completed' },
                                { id: 'cancelled', name: 'Cancelled' },
                            ]}
                            defaultValue="new"
                            validate={required()}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <ReferenceInput source="technician_id" reference="technicians">
                            <AutocompleteInput
                                optionText={(choice: any) => `${choice.first_name} ${choice.last_name}`}
                                fullWidth
                            />
                        </ReferenceInput>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <DateTimeInput source="scheduled_date" fullWidth />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <NumberInput
                            source="estimated_duration"
                            label="Estimated Duration (minutes)"
                            min={0}
                            fullWidth
                        />
                    </Grid>
                </Grid>

                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                    Cost Estimates
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                        <NumberInput
                            source="parts_cost"
                            label="Parts Cost (PLN)"
                            min={0}
                            step={0.01}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <NumberInput
                            source="labor_cost"
                            label="Labor Cost (PLN)"
                            min={0}
                            step={0.01}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <NumberInput
                            source="total_cost"
                            label="Total Cost (PLN)"
                            min={0}
                            step={0.01}
                            fullWidth
                        />
                    </Grid>
                </Grid>
            </SimpleForm>
        </Create>
    );
};
