import { Grid, Card, CardContent, Typography, Box, Chip, useTheme, alpha } from '@mui/material';
import {
    Build as ServiceIcon,
    AcUnit as EquipmentIcon,
    Engineering as TechnicianIcon,
    Warning as WarningIcon,
    CheckCircle as CheckIcon,
    Schedule as ScheduleIcon,
    TrendingUp as TrendingUpIcon,
    TrendingDown as TrendingDownIcon,
    AttachMoney as MoneyIcon,
    Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useGetList } from 'react-admin';
import { ServiceOrder, Equipment, Technician, MaintenanceSchedule, Quote, Invoice, PartsInventory } from '../types';
import { CosmicCard, CosmicMetricCard } from '../components/cosmic/CosmicCard';
import { ServiceOrderStatus, PriorityIndicator, EquipmentStatus } from '../components/cosmic/CosmicStatusIndicator';

const StatCard = ({ 
    title, 
    value, 
    icon, 
    color = '#2196f3',
    subtitle 
}: {
    title: string;
    value: number | string;
    icon: React.ReactNode;
    color?: string;
    subtitle?: string;
}) => (
    <Card>
        <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                        {title}
                    </Typography>
                    <Typography variant="h4" component="div">
                        {value}
                    </Typography>
                    {subtitle && (
                        <Typography variant="body2" color="textSecondary">
                            {subtitle}
                        </Typography>
                    )}
                </Box>
                <Box sx={{ color, fontSize: 40 }}>
                    {icon}
                </Box>
            </Box>
        </CardContent>
    </Card>
);

const ServiceOrdersOverview = () => {
    const { data: serviceOrders, total, isPending } = useGetList<ServiceOrder>('service_orders', {
        pagination: { page: 1, perPage: 1000 },
    });
    const theme = useTheme();

    if (isPending) return <div>Loading...</div>;

    const statusCounts = serviceOrders?.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
    }, {} as Record<string, number>) || {};

    const urgentOrders = serviceOrders?.filter(order => order.priority === 'urgent').length || 0;
    const todayOrders = serviceOrders?.filter(order => {
        const today = new Date().toISOString().split('T')[0];
        return order.scheduled_date?.startsWith(today);
    }).length || 0;

    // Calculate revenue
    const totalRevenue = serviceOrders?.reduce((sum, order) => sum + (order.total_cost || 0), 0) || 0;
    const completedRevenue = serviceOrders?.filter(order => order.status === 'completed')
        .reduce((sum, order) => sum + (order.total_cost || 0), 0) || 0;

    // Calculate trends (mock data for demo)
    const previousTotal = Math.floor((total || 0) * 0.85); // 15% growth
    const growthRate = (total || 0) > 0 ? (((total || 0) - previousTotal) / previousTotal * 100).toFixed(1) : '0';

    return (
        <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Total Service Orders"
                    value={total || 0}
                    icon={<ServiceIcon />}
                    color="primary"
                    loading={isPending}
                    trend="up"
                    trendValue={`+${growthRate}%`}
                    subtitle="vs last month"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="New Orders"
                    value={statusCounts.new || 0}
                    icon={<AssignmentIcon />}
                    color="warning"
                    loading={isPending}
                    subtitle="Awaiting scheduling"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="In Progress"
                    value={statusCounts.in_progress || 0}
                    icon={<ServiceIcon />}
                    color="success"
                    loading={isPending}
                    subtitle="Currently active"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Urgent Orders"
                    value={urgentOrders}
                    icon={<WarningIcon />}
                    color="error"
                    loading={isPending}
                    subtitle="Require immediate attention"
                />
            </Grid>

            {/* Second Row */}
            <Grid item xs={12} md={4}>
                <CosmicMetricCard
                    title="Scheduled Today"
                    value={todayOrders}
                    icon={<ScheduleIcon />}
                    color="secondary"
                    loading={isPending}
                    subtitle="Orders for today"
                />
            </Grid>
            <Grid item xs={12} md={4}>
                <CosmicMetricCard
                    title="Completed"
                    value={statusCounts.completed || 0}
                    icon={<CheckIcon />}
                    color="success"
                    loading={isPending}
                    trend="up"
                    trendValue="+12%"
                    subtitle="Successfully finished"
                />
            </Grid>
            <Grid item xs={12} md={4}>
                <CosmicMetricCard
                    title="Total Revenue"
                    value={`${(totalRevenue / 1000).toFixed(1)}k PLN`}
                    icon={<MoneyIcon />}
                    color="success"
                    loading={isPending}
                    trend="up"
                    trendValue="+8.5%"
                    subtitle="All orders combined"
                />
            </Grid>
        </Grid>
    );
};

const EquipmentOverview = () => {
    const { data: equipment, total, isPending } = useGetList<Equipment>('equipment', {
        pagination: { page: 1, perPage: 1000 },
    });

    if (isPending) return <div>Loading...</div>;

    const statusCounts = equipment?.reduce((acc, item) => {
        acc[item.status] = (acc[item.status] || 0) + 1;
        return acc;
    }, {} as Record<string, number>) || {};

    const warrantyExpiring = equipment?.filter(item => {
        if (!item.warranty_expiry) return false;
        const today = new Date();
        const warrantyDate = new Date(item.warranty_expiry);
        const daysUntilExpiry = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
    }).length || 0;

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Total Equipment"
                    value={total || 0}
                    icon={<EquipmentIcon />}
                    color="#2196f3"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Active"
                    value={statusCounts.active || 0}
                    icon={<CheckIcon />}
                    color="#4caf50"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Needs Service"
                    value={statusCounts.needs_service || 0}
                    icon={<WarningIcon />}
                    color="#f44336"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Warranty Expiring"
                    value={warrantyExpiring}
                    icon={<WarningIcon />}
                    color="#ff9800"
                    subtitle="Within 30 days"
                />
            </Grid>
        </Grid>
    );
};

const TechniciansOverview = () => {
    const { data: technicians, total, isPending } = useGetList<Technician>('technicians', {
        pagination: { page: 1, perPage: 1000 },
    });

    if (isPending) return <div>Loading...</div>;

    const activeTechnicians = technicians?.filter(tech => tech.status === 'active').length || 0;
    const onLeave = technicians?.filter(tech => tech.status === 'on_leave').length || 0;

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
                <StatCard
                    title="Total Technicians"
                    value={total || 0}
                    icon={<TechnicianIcon />}
                    color="#2196f3"
                />
            </Grid>
            <Grid item xs={12} md={4}>
                <StatCard
                    title="Active"
                    value={activeTechnicians}
                    icon={<CheckIcon />}
                    color="#4caf50"
                />
            </Grid>
            <Grid item xs={12} md={4}>
                <StatCard
                    title="On Leave"
                    value={onLeave}
                    icon={<WarningIcon />}
                    color="#ff9800"
                />
            </Grid>
        </Grid>
    );
};

// Quotes Overview Component
const QuotesOverview = () => {
    const { data: quotes, total, isPending } = useGetList<Quote>('quotes', {
        pagination: { page: 1, perPage: 1000 },
    });

    if (isPending) return <div>Loading...</div>;

    const statusCounts = quotes?.reduce((acc, quote) => {
        acc[quote.status] = (acc[quote.status] || 0) + 1;
        return acc;
    }, {} as Record<string, number>) || {};

    const totalValue = quotes?.reduce((sum, quote) => sum + (quote.total_amount || 0), 0) || 0;
    const acceptedValue = quotes?.filter(quote => quote.status === 'accepted')
        .reduce((sum, quote) => sum + (quote.total_amount || 0), 0) || 0;

    return (
        <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Total Quotes"
                    value={total || 0}
                    icon={<AssignmentIcon />}
                    color="primary"
                    loading={isPending}
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Pending Quotes"
                    value={statusCounts.sent || 0}
                    icon={<ScheduleIcon />}
                    color="warning"
                    loading={isPending}
                    subtitle="Awaiting response"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Accepted Quotes"
                    value={statusCounts.accepted || 0}
                    icon={<CheckIcon />}
                    color="success"
                    loading={isPending}
                    trend="up"
                    trendValue="+15%"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Quote Value"
                    value={`${(totalValue / 1000).toFixed(1)}k PLN`}
                    icon={<MoneyIcon />}
                    color="success"
                    loading={isPending}
                    subtitle="Total pipeline"
                />
            </Grid>
        </Grid>
    );
};

// Parts Inventory Overview Component
const PartsOverview = () => {
    const { data: parts, total, isPending } = useGetList<PartsInventory>('parts_inventory', {
        pagination: { page: 1, perPage: 1000 },
    });

    if (isPending) return <div>Loading...</div>;

    const lowStockParts = parts?.filter(part =>
        part.quantity_in_stock <= part.minimum_stock_level
    ).length || 0;

    const outOfStockParts = parts?.filter(part =>
        part.quantity_in_stock === 0
    ).length || 0;

    const totalValue = parts?.reduce((sum, part) =>
        sum + ((part.unit_cost || 0) * part.quantity_in_stock), 0
    ) || 0;

    return (
        <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Total Parts"
                    value={total || 0}
                    icon={<ServiceIcon />}
                    color="primary"
                    loading={isPending}
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Low Stock"
                    value={lowStockParts}
                    icon={<WarningIcon />}
                    color="warning"
                    loading={isPending}
                    subtitle="Need reordering"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Out of Stock"
                    value={outOfStockParts}
                    icon={<WarningIcon />}
                    color="error"
                    loading={isPending}
                    subtitle="Critical shortage"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <CosmicMetricCard
                    title="Inventory Value"
                    value={`${(totalValue / 1000).toFixed(1)}k PLN`}
                    icon={<MoneyIcon />}
                    color="success"
                    loading={isPending}
                    subtitle="Total stock value"
                />
            </Grid>
        </Grid>
    );
};

export const HVACDashboard = () => {
    return (
        <Box sx={{ p: 2 }}>
            <Typography variant="h4" gutterBottom>
                HVAC Management Dashboard
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Service Orders
            </Typography>
            <ServiceOrdersOverview />

            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                Quotes & Sales
            </Typography>
            <QuotesOverview />

            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                Equipment
            </Typography>
            <EquipmentOverview />

            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                Parts Inventory
            </Typography>
            <PartsOverview />

            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                Technicians
            </Typography>
            <TechniciansOverview />
        </Box>
    );
};
